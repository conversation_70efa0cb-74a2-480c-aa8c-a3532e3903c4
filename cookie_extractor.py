#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台Cookie提取工具
支持头条、百家、抖音、B站、闲鱼、快手等平台的Cookie提取
"""

import os
import json
import time
import threading
from datetime import datetime
from tkinter import filedialog, messagebox
import customtkinter as ctk
from DrissionPage import ChromiumPage, ChromiumOptions


class CookieExtractor:
    """Cookie提取器核心类"""
    
    def __init__(self):
        self.page = None
        self.platforms = {
            "头条": {"url": "https://mp.toutiao.com/profile_v4/index", "name": "toutiao"},
            "百家": {"url": "https://baijiahao.baidu.com/", "name": "baijiahao"},
            "抖音": {"url": "https://creator.douyin.com/", "name": "douyin"},
            "B站": {"url": "https://member.bilibili.com/", "name": "bilibili"},
            "闲鱼": {"url": "https://www.goofish.com/", "name": "xianyu"},
            "快手": {"url": "https://cp.kuaishou.com/", "name": "kuaishou"}
        }
    
    def start_browser(self, platform_url):
        """启动浏览器并打开指定平台"""
        try:
            # 配置浏览器选项
            options = ChromiumOptions()
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--disable-web-security')
            options.set_argument('--allow-running-insecure-content')
            options.set_argument('--disable-features=VizDisplayCompositor')
            options.set_argument('--disable-extensions')
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            # 禁用安全警告
            options.set_argument('--disable-infobars')
            options.set_argument('--disable-notifications')
            options.set_argument('--disable-popup-blocking')
            options.set_argument('--disable-translate')
            options.set_argument('--disable-background-timer-throttling')
            options.set_argument('--disable-backgrounding-occluded-windows')
            options.set_argument('--disable-renderer-backgrounding')
            options.set_argument('--disable-features=TranslateUI')
            options.set_argument('--disable-ipc-flooding-protection')

            # 创建页面对象
            self.page = ChromiumPage(addr_or_opts=options)

            # 启用Network域以支持Cookie获取
            try:
                self.page.run_cdp('Network.enable')
            except:
                pass  # 如果失败也继续执行

            # 打开指定平台
            self.page.get(platform_url)
            return True

        except Exception as e:
            print(f"启动浏览器失败: {e}")
            return False
    
    def extract_cookies(self):
        """提取当前页面的Cookie"""
        if not self.page:
            return None

        try:
            # 使用CDP协议获取所有Cookie
            result = self.page.run_cdp('Network.getAllCookies')
            if result and 'cookies' in result:
                return result['cookies']
            else:
                return None

        except Exception as e:
            print(f"提取Cookie失败: {e}")
            return None
    
    def format_cookies_to_txt(self, cookies, platform_name):
        """将Cookie格式化为纯cookie字符串格式"""
        if not cookies:
            return ""

        # 过滤重要的cookie
        important_cookies = []
        for cookie in cookies:
            name = cookie.get('name', '')
            domain = cookie.get('domain', '')

            # 只保留目标平台相关的重要cookie
            if platform_name == "toutiao":
                # 头条平台重要cookie
                important_names = [
                    'ttwid', 'sso_uid_tt', 'sso_uid_tt_ss', 'toutiao_sso_user',
                    'toutiao_sso_user_ss', 'sid_ucp_sso_v1', 'ssid_ucp_sso_v1',
                    'odin_tt', 'passport_auth_status', 'passport_auth_status_ss',
                    'sid_guard', 'uid_tt', 'uid_tt_ss', 'sid_tt', 'sessionid',
                    'sessionid_ss', 'session_tlb_tag', 'sid_ucp_v1', 'ssid_ucp_v1',
                    'gfkadpd', 'csrf_session_id', 'tt_webid', 'n_mh', 'is_staff_user'
                ]
                if (name in important_names and
                    ('toutiao.com' in domain or 'mp.toutiao.com' in domain)):
                    important_cookies.append(cookie)
            else:
                # 其他平台保留所有相关域名的cookie
                important_cookies.append(cookie)

        # 生成cookie字符串
        cookie_pairs = []
        for cookie in important_cookies:
            name = cookie.get('name', '')
            value = cookie.get('value', '')
            if name and value:
                cookie_pairs.append(f"{name}={value}")

        return "; ".join(cookie_pairs)
    
    def get_account_name(self, platform_name):
        """获取当前登录的账号名"""
        try:
            if platform_name == "toutiao":
                # 头条平台获取用户名
                js_code = """
                // 从页面中获取用户名
                const pageText = document.body.innerText;
                const userMatches = pageText.match(/([\\u4e00-\\u9fa5a-zA-Z0-9_-]{2,20})(?=\\s*主页|\\s*创作|\\s*管理|\\s*数据)/);
                if (userMatches && userMatches[1]) {
                    return userMatches[1];
                }

                // 尝试其他方式获取用户名
                const userElements = document.querySelectorAll('.user-name, .username, [class*="user"], [class*="name"]');
                for (let elem of userElements) {
                    const text = elem.textContent || elem.innerText;
                    if (text && text.trim().length > 0 && text.trim().length < 20) {
                        return text.trim();
                    }
                }

                return "未知用户";
                """
                result = self.page.run_js(js_code)
                return result if result and result != "未知用户" else "未知用户"
            else:
                # 其他平台的用户名获取逻辑可以后续添加
                return "未知用户"

        except Exception as e:
            print(f"获取账号名失败: {e}")
            return "未知用户"

    def save_cookies(self, cookies_txt, save_path, platform_name, account_name=None):
        """保存Cookie到文件"""
        try:
            if not account_name:
                account_name = self.get_account_name(platform_name)

            # 使用账号名作为文件名
            filename = f"{account_name}.txt"
            filepath = os.path.join(save_path, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(cookies_txt)

            return filepath

        except Exception as e:
            print(f"保存Cookie失败: {e}")
            return None
    
    def logout_current_account(self, platform_name):
        """退出当前账号（针对不同平台的专用方法）"""
        try:
            # 根据不同平台使用不同的退出策略
            if platform_name == "toutiao":
                return self._logout_toutiao()
            elif platform_name == "baijiahao":
                return self._logout_baijiahao()
            elif platform_name == "douyin":
                return self._logout_douyin()
            elif platform_name == "bilibili":
                return self._logout_bilibili()
            elif platform_name == "xianyu":
                return self._logout_xianyu()
            elif platform_name == "kuaishou":
                return self._logout_kuaishou()
            else:
                return self._logout_generic()

        except Exception as e:
            print(f"退出账号失败: {e}")
            return False

    def _logout_toutiao(self):
        """头条平台退出 - 使用验证过的方法"""
        try:
            # 使用JavaScript直接清除cookie的方法
            clear_js = """
            // 清除所有cookie
            function clearAllCookies() {
                const cookies = document.cookie.split(";");

                for (let cookie of cookies) {
                    const eqPos = cookie.indexOf("=");
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

                    // 清除cookie - 设置过期时间为过去的时间
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.toutiao.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=toutiao.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.mp.toutiao.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=mp.toutiao.com";
                }

                return "Cookies cleared";
            }

            clearAllCookies();

            // 清除localStorage和sessionStorage
            localStorage.clear();
            sessionStorage.clear();

            // 验证cookie是否已清除
            return {
                message: "Cookie和存储清除完成",
                remainingCookies: document.cookie,
                localStorageCleared: Object.keys(localStorage).length === 0,
                sessionStorageCleared: Object.keys(sessionStorage).length === 0
            };
            """

            result = self.page.run_js(clear_js)
            print(f"头条退出结果: {result}")

            # 等待一下然后刷新页面
            time.sleep(1)
            self.page.refresh()
            time.sleep(2)

            return True

        except Exception as e:
            print(f"头条退出失败: {e}")
            return False

    def _logout_baijiahao(self):
        """百家号平台退出"""
        selectors = [
            '.user-info',
            '.user-avatar',
            '.header-user'
        ]

        for selector in selectors:
            try:
                user_area = self.page.ele(selector, timeout=3)
                if user_area:
                    user_area.click()
                    time.sleep(1)

                    logout_btn = self.page.ele('text:退出', timeout=2)
                    if logout_btn:
                        logout_btn.click()
                        time.sleep(2)
                        return True
            except:
                continue

        return self._logout_generic()

    def _logout_douyin(self):
        """抖音创作者平台退出"""
        try:
            # 抖音通常在右上角有用户头像
            user_avatar = self.page.ele('.user-avatar, .header-avatar', timeout=3)
            if user_avatar:
                user_avatar.click()
                time.sleep(1)

                logout_btn = self.page.ele('text:退出登录', timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
        except:
            pass

        return self._logout_generic()

    def _logout_bilibili(self):
        """B站退出"""
        try:
            # B站用户头像通常在右上角
            user_face = self.page.ele('.user-face, .header-avatar', timeout=3)
            if user_face:
                user_face.click()
                time.sleep(1)

                logout_btn = self.page.ele('text:退出登录', timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
        except:
            pass

        return self._logout_generic()

    def _logout_xianyu(self):
        """闲鱼平台退出"""
        try:
            # 闲鱼用户信息区域
            user_info = self.page.ele('.user-info, .user-avatar', timeout=3)
            if user_info:
                user_info.click()
                time.sleep(1)

                logout_btn = self.page.ele('text:退出', timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
        except:
            pass

        return self._logout_generic()

    def _logout_kuaishou(self):
        """快手创作者平台退出"""
        try:
            # 快手用户头像
            user_avatar = self.page.ele('.user-avatar, .header-user', timeout=3)
            if user_avatar:
                user_avatar.click()
                time.sleep(1)

                logout_btn = self.page.ele('text:退出登录', timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
        except:
            pass

        return self._logout_generic()

    def _logout_generic(self):
        """通用退出方法"""
        # 尝试查找常见的退出按钮
        logout_selectors = [
            'text:退出登录',
            'text:退出',
            'text:登出',
            'text:退出账号',
            'a[href*="logout"]',
            'button[onclick*="logout"]',
            '.logout',
            '.sign-out',
            '[data-action="logout"]',
            '[title*="退出"]',
            '[alt*="退出"]'
        ]

        for selector in logout_selectors:
            try:
                logout_btn = self.page.ele(selector, timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
            except:
                continue

        return False

    def clear_cookies_and_logout(self, platform_name):
        """清除Cookie并退出账号"""
        try:
            # 直接使用平台特定的退出方法
            logout_success = self.logout_current_account(platform_name)
            return logout_success

        except Exception as e:
            print(f"清除Cookie并退出失败: {e}")
            return False

    def close_browser(self):
        """关闭浏览器"""
        if self.page:
            try:
                self.page.quit()
                self.page = None
            except:
                pass


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        
        # 默认配置
        return {
            "save_path": os.path.expanduser("~/Desktop"),
            "selected_platform": "头条"
        }
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def get(self, key, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置项"""
        self.config[key] = value
        self.save_config()


class CookieExtractorGUI:
    """GUI界面类"""
    
    def __init__(self):
        # 设置外观
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        # 初始化组件
        self.extractor = CookieExtractor()
        self.config_manager = ConfigManager()
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("多平台Cookie提取工具")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 界面变量
        self.selected_platform = ctk.StringVar(value=self.config_manager.get("selected_platform", "头条"))
        self.save_path = ctk.StringVar(value=self.config_manager.get("save_path", os.path.expanduser("~/Desktop")))
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = ctk.CTkLabel(
            self.root, 
            text="多平台Cookie提取工具", 
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 平台选择框
        platform_frame = ctk.CTkFrame(self.root)
        platform_frame.pack(pady=10, padx=20, fill="x")
        
        platform_label = ctk.CTkLabel(platform_frame, text="选择平台:", font=ctk.CTkFont(size=14))
        platform_label.pack(pady=(10, 5))
        
        # 平台单选按钮
        platforms_container = ctk.CTkFrame(platform_frame)
        platforms_container.pack(pady=(0, 10), padx=10, fill="x")
        
        platforms = ["头条", "百家", "抖音", "B站", "闲鱼", "快手"]
        for i, platform in enumerate(platforms):
            row = i // 3
            col = i % 3
            
            radio_btn = ctk.CTkRadioButton(
                platforms_container,
                text=platform,
                variable=self.selected_platform,
                value=platform,
                command=self.on_platform_changed
            )
            radio_btn.grid(row=row, column=col, padx=10, pady=5, sticky="w")
        
        # 保存路径选择
        path_frame = ctk.CTkFrame(self.root)
        path_frame.pack(pady=10, padx=20, fill="x")
        
        path_label = ctk.CTkLabel(path_frame, text="保存路径:", font=ctk.CTkFont(size=14))
        path_label.pack(pady=(10, 5))
        
        path_container = ctk.CTkFrame(path_frame)
        path_container.pack(pady=(0, 10), padx=10, fill="x")
        
        self.path_entry = ctk.CTkEntry(path_container, textvariable=self.save_path, width=300)
        self.path_entry.pack(side="left", padx=(0, 10), fill="x", expand=True)
        
        browse_btn = ctk.CTkButton(path_container, text="浏览", command=self.browse_save_path, width=80)
        browse_btn.pack(side="right")
        
        # 操作按钮
        button_frame = ctk.CTkFrame(self.root)
        button_frame.pack(pady=20, padx=20, fill="x")
        
        self.start_browser_btn = ctk.CTkButton(
            button_frame,
            text="启动浏览器",
            command=self.start_browser_thread,
            width=150,
            height=40
        )
        self.start_browser_btn.pack(side="left", padx=(10, 5))
        
        self.extract_cookies_btn = ctk.CTkButton(
            button_frame,
            text="获取CK并退出",
            command=self.extract_cookies_thread,
            width=150,
            height=40,
            state="disabled"
        )
        self.extract_cookies_btn.pack(side="right", padx=(5, 10))


        
        # 状态显示区域
        status_frame = ctk.CTkFrame(self.root)
        status_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        status_label = ctk.CTkLabel(status_frame, text="状态信息:", font=ctk.CTkFont(size=14))
        status_label.pack(pady=(10, 5))
        
        self.status_text = ctk.CTkTextbox(status_frame, height=100)
        self.status_text.pack(pady=(0, 10), padx=10, fill="both", expand=True)
        
        # 初始状态
        self.log_status("程序已启动，请选择平台并点击'启动浏览器'")
    
    def log_status(self, message):
        """记录状态信息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        self.status_text.insert("end", log_message)
        self.status_text.see("end")
        self.root.update()
    
    def on_platform_changed(self):
        """平台选择改变时的回调"""
        self.config_manager.set("selected_platform", self.selected_platform.get())
        self.log_status(f"已选择平台: {self.selected_platform.get()}")
    
    def browse_save_path(self):
        """浏览保存路径"""
        path = filedialog.askdirectory(initialdir=self.save_path.get())
        if path:
            self.save_path.set(path)
            self.config_manager.set("save_path", path)
            self.log_status(f"保存路径已设置: {path}")
    
    def start_browser_thread(self):
        """在新线程中启动浏览器"""
        threading.Thread(target=self.start_browser, daemon=True).start()
    
    def start_browser(self):
        """启动浏览器"""
        try:
            self.start_browser_btn.configure(state="disabled")
            self.log_status("正在启动浏览器...")
            
            platform = self.selected_platform.get()
            platform_url = self.extractor.platforms[platform]["url"]
            
            success = self.extractor.start_browser(platform_url)
            
            if success:
                self.log_status(f"浏览器已启动，已打开{platform}平台")
                self.log_status("请在浏览器中完成登录，然后点击'获取CK并退出'")
                self.extract_cookies_btn.configure(state="normal")
            else:
                self.log_status("启动浏览器失败")
                self.start_browser_btn.configure(state="normal")
                
        except Exception as e:
            self.log_status(f"启动浏览器出错: {e}")
            self.start_browser_btn.configure(state="normal")
    
    def extract_cookies_thread(self):
        """在新线程中提取Cookie"""
        threading.Thread(target=self.extract_cookies, daemon=True).start()
    
    def extract_cookies(self):
        """提取Cookie并退出账号"""
        try:
            self.extract_cookies_btn.configure(state="disabled")
            self.log_status("正在提取Cookie...")

            # 提取Cookie
            cookies = self.extractor.extract_cookies()

            if not cookies:
                self.log_status("未能获取到Cookie，请确保已登录")
                self.extract_cookies_btn.configure(state="normal")
                return

            # 格式化Cookie
            platform = self.selected_platform.get()
            platform_name = self.extractor.platforms[platform]["name"]
            cookies_txt = self.extractor.format_cookies_to_txt(cookies, platform_name)

            # 获取账号名并保存Cookie
            save_path = self.save_path.get()
            account_name = self.extractor.get_account_name(platform_name)
            filepath = self.extractor.save_cookies(cookies_txt, save_path, platform_name, account_name)

            if filepath:
                self.log_status(f"✓ Cookie已保存到: {filepath}")
                self.log_status(f"✓ 账号名: {account_name}")
                self.log_status(f"✓ 共提取到 {len(cookies)} 个Cookie")

                # 立即退出账号并清除Cookie
                self.log_status("正在退出当前账号并清除Cookie...")
                logout_success = self.extractor.clear_cookies_and_logout(platform_name)

                if logout_success:
                    self.log_status("✓ 已成功退出当前账号并清除Cookie")
                    # 等待页面跳转完成
                    time.sleep(3)
                    # 验证是否真的退出了
                    try:
                        current_url = self.extractor.page.url
                        if any(keyword in current_url.lower() for keyword in ['login', 'signin', 'auth']):
                            self.log_status("✓ 确认已退出到登录页面")
                        else:
                            self.log_status("✓ 已清除Cookie，建议检查登录状态")
                    except:
                        self.log_status("✓ 退出操作已完成")
                else:
                    self.log_status("⚠ 自动退出失败，已尝试清除Cookie")
                    self.log_status("建议：手动检查登录状态或重启浏览器")

                # 成功完成所有操作
                success_msg = f"Cookie提取和账号退出完成！\n\n账号名: {account_name}\n保存位置: {filepath}\nCookie数量: {len(cookies)}个\n账号状态: 已退出"
                messagebox.showinfo("操作完成", success_msg)

                # 重置按钮状态，允许用户进行下一次操作
                self.log_status("=" * 50)
                self.log_status("操作完成！可以重新登录其他账号或切换平台")

            else:
                self.log_status("保存Cookie失败")

            self.extract_cookies_btn.configure(state="normal")

        except Exception as e:
            self.log_status(f"操作出错: {e}")
            self.extract_cookies_btn.configure(state="normal")
    
    def run(self):
        """运行GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """关闭程序时的清理工作"""
        try:
            self.extractor.close_browser()
        except:
            pass
        self.root.destroy()


if __name__ == "__main__":
    app = CookieExtractorGUI()
    app.run()
